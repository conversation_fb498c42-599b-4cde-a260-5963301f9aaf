class Attribute{
    // Instance Attribute
    // Belongs to an instance of the class
    // Each instance of the class has its own copy of the field
    int iValue = 10; 

    // Final Attribute. Constant value, cannot be changed
    // final String sValue = "Hello"; // Final field, cannot be changed

    // Static Attribute. Belongs to the class, not instances
    // All instances of the class share the same static field
    static int sStaticValue = 30;
    Attribute(){
        sStaticValue++;
    }
}
public class B_Attribute {
    public static void main(String[] args) {
        // Create an instance of the Field class
        Attribute field = new Attribute();
        // Access and print the value of iValue
        System.out.println("Value of iValue: " + field.iValue);
        System.out.println("Value of static field sStaticValue: " + field.sStaticValue);    
        field.iValue = 20; // Overriding the field value
        System.out.println("Modified value of iValue: " + field.iValue);
        // Create another instance of the Field class
        Attribute field2 = new Attribute();
        // field.sValue = "World"; // This line will cause an error because sValue is final
        // Access and print the value of iValue in the new instance
        // System.out.println("Value of iValue in field2: " + field2.iValue);
        System.out.println("Value of static field sStaticValue in field2: " + field2.sStaticValue);
    }
}
