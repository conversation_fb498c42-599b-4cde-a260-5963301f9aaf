class I_ConstructorOverloading{
	int id = 110;
	String name = "KAVIN KUMAR C";
	int age = 17;
	// This is a default constructor.
	// It initializes the data members with default values.
	I_ConstructorOverloading(){
		System.out.println("Default constructor called.");
	}
	// This is a parameterized constructor.
	// It initializes the data members with the given 2 parameters.
	I_ConstructorOverloading(int id,String name){
		this.id = id;
		this.name = name;
	}
	// This is another parameterized constructor.
	// It initializes the data members with the given 3 parameters.
	I_ConstructorOverloading(int i,String n,int a){
		this.id = i;
		this.name = n;
		this.age = a;
	}
	void display(){
		System.out.println(id + " " + name + " " + age);	
	}
	public static void main(String args[]){
		I_ConstructorOverloading s = new I_ConstructorOverloading();
		I_ConstructorOverloading s1 = new I_ConstructorOverloading (79,"HARISH G");
		I_ConstructorOverloading s2 = new I_ConstructorOverloading(131,"MANOJ V",18);
		s.display();
		s1.display();
		s2.display();
	}
}