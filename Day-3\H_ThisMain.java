public class H_ThisMain {
  int x = 10;

  // Constructor with a parameter
  public H_ThisMain(int x) {
    this.x = x;
    // if this keyword is not used, it would refer to the parameter x,
    // which would not change the instance variable x.
    // x = x;
  }

  // Call the constructor
  public static void main(String[] args) {
    H_ThisMain myObj = new H_ThisMain(5);
    System.out.println("Value of x = " + myObj.x);
  }
}